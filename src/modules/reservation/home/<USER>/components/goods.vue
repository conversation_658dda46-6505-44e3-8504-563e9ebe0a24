<template>
  <view class="p-4">
    <van-empty :image="image" description="暂无商品">
      <div class="text-sm text-gray-500 mb-4">请选择您要入库的商品</div>
      <div
        class="flex items-center justify-center rounded-full bg-theme-blue px-6 py-2.5 text-sm font-medium text-white shadow-md transition-all duration-300 hover:shadow-lg active:scale-95 active:bg-theme-blue-600"
        @click="handleSelectGoods"
      >
        <i class="i-mdi-package-variant-closed mr-1.5" />
        选择商品
      </div>
    </van-empty>

    <ui-popup v-model:show="showGoodsSelector" title="选择商品" position="bottom">
      <div class="px-4 bg-white">
        <uni-search-bar v-model="searchValue" placeholder="请输入药材名称" @confirm="handleSearch" @clear="handleClear" cancelButton="none" />
      </div>
      <div class="px-4 pt-2 pr-5">
        <van-index-bar :index-list="indexList">
          <view v-for="item in listLetter" :key="item.letter">
            <van-index-anchor :index="item.letter" />
            <div class="grid grid-cols-4 gap-3 my-2">
              <div v-for="item in item.data" :key="item.id" @click="handleItemClick(item)" class="bg-white rounded-lg p-3 shadow-sm active:bg-green-200/30 flex flex-col h-16">
                <div class="text-sm font-medium text-gray-800">{{ item.bzmc }}</div>
                <div class="text-xs text-gray-500 mt-1 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
              </div>
            </div>
          </view>
        </van-index-bar>
      </div>
    </ui-popup>
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  const image = ref(monkey.$url.cdn('RElSRV8zODcyYmM1NWMyOTc0M2VlYTRlMDJkMjhiNTI0MDBhMuaaguaXoOWGheWuuS5wbmc='));

  // 是否显示商品选择器
  const showGoodsSelector = ref(false);

  // 定义触发事件
  const emit = defineEmits(['selectGoods']);

  // 处理选择商品事件
  const handleSelectGoods = () => {
    showGoodsSelector.value = true;
  };
</script>

<style lang="scss" scoped></style>
