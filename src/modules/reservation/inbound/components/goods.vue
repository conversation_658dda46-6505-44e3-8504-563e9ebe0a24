<template>
  <view class="flex flex-col size-full">
    <!-- 已选商品信息展示区域 -->
    <div v-if="confirmedGoods.length === 0" class="flex justify-center flex-1 items-center">
      <van-empty :image="image" description="暂无商品">
        <div class="text-sm text-gray-500 mb-4">请选择您要入库的商品</div>
      </van-empty>
    </div>

    <!-- 显示选中商品信息可输入数量和重量 -->
    <div v-else class="flex flex-col">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between mb-3 flex-shrink-0">
        <div class="flex items-center">
          <i class="i-mdi-package-variant mr-2 text-theme-blue" />
          <text class="text-base font-medium text-gray-800">已选商品</text>
        </div>
        <div class="text-sm text-gray-500">共 {{ confirmedGoods.length }} 件</div>
      </div>

      <!-- 可滚动的商品列表 -->
      <scroll-view scroll-y class="flex-1 size-full">
        <div class="space-y-2 pb-4">
          <div v-for="(item, index) in confirmedGoods" :key="item.id" class="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
            <!-- 商品基本信息 -->
            <div class="flex items-center justify-between mb-2">
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-800 truncate">{{ item.bzmc }}</div>
                <div class="text-xs text-gray-500 mt-0.5 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
              </div>
              <div @click="handleRemoveGood(index)" class="w-6 h-6 flex items-center justify-center rounded-full bg-red-50 text-red-500 active:bg-red-100 transition-colors ml-2 flex-shrink-0">
                <i class="i-mdi-close text-xs" />
              </div>
            </div>

            <!-- 数量和重量输入 -->
            <div class="grid grid-cols-2 gap-2">
              <!-- 数量输入 -->
              <div class="space-y-1">
                <label class="text-xs font-medium text-gray-700">数量</label>
                <van-stepper v-model="item.quantity" :min="1" :max="999" :step="1" integer @change="handleQuantityChange(index, $event)" class="w-full" button-size="24px" input-width="40px" />
                <div class="text-xs text-gray-500">单位：件</div>
              </div>

              <!-- 重量输入 -->
              <div class="space-y-1">
                <label class="text-xs font-medium text-gray-700">重量</label>
                <div class="relative">
                  <input
                    v-model.number="item.weight"
                    type="number"
                    step="0.1"
                    min="0"
                    placeholder="0.0"
                    class="w-full h-8 px-2 pr-6 border border-gray-200 rounded-lg outline-none text-xs focus:border-theme-blue focus:ring-1 focus:ring-theme-blue"
                    @input="validateWeight(index)"
                  />
                  <text class="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">kg</text>
                </div>
                <div class="text-xs text-gray-500">单位：千克</div>
              </div>
            </div>
          </div>
        </div>
      </scroll-view>
    </div>
    <!-- 选择商品按钮 -->
    <div class="flex justify-center mb-4">
      <div
        class="flex items-center justify-center rounded-full bg-theme-blue px-6 py-2.5 text-sm font-medium text-white shadow-md transition-all duration-300 hover:shadow-lg active:scale-95 active:bg-theme-blue-600"
        @click="handleSelectGoods"
      >
        <i class="i-mdi-package-variant-closed mr-1.5" />
        选择商品
      </div>
    </div>
    <ui-popup v-model:show="showGoodsSelector" title="选择物品" position="bottom" :safe-bottom="false">
      <view class="h-[82vh] flex flex-col bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="px-4 bg-white">
          <uni-search-bar v-model="goodsSearchValue" placeholder="请输入药材名称关键字" @confirm="handleGoodsSearch" @clear="handleGoodsClear" cancelButton="none" />
        </div>
        <div class="flex-1 overflow-hidden">
          <scroll-view scroll-y class="h-full" v-if="goodsListLetter.length > 0">
            <div class="px-4 pt-2 box-border">
              <van-index-bar :index-list="goodsIndexList" :sticky="false">
                <view v-for="item in goodsListLetter" :key="item.letter">
                  <text class="text-sm font-medium text-gray-800">{{ item.letter }}</text>
                  <div class="grid grid-cols-4 gap-3 my-2">
                    <div
                      v-for="item in item.data"
                      :key="item.id"
                      @click="handleGoodsItemClick(item)"
                      class="bg-white rounded-lg p-3 shadow-sm active:bg-green-200/30 flex flex-col h-16 relative transition-all duration-200"
                      :class="{ 'ring-2 ring-theme-blue bg-blue-50': isSelected(item.id) }"
                    >
                      <div class="text-sm font-medium text-gray-800">{{ item.bzmc }}</div>
                      <div class="text-xs text-gray-500 mt-1 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
                      <!-- 选中状态指示器 -->
                      <div v-if="isSelected(item.id)" class="absolute -top-1 -right-1 w-5 h-5 bg-theme-blue rounded-full flex items-center justify-center">
                        <i class="i-mdi-check text-white text-xs" />
                      </div>
                    </div>
                  </div>
                </view>
              </van-index-bar>
            </div>
          </scroll-view>
        </div>
        <!-- 底部确认区域 -->
        <div class="px-4 py-3 bg-white border-t border-gray-100 shadow-lg rounded-t-lg" :class="`pb-[env(safe-area-inset-bottom)]`">
          <!-- 已选商品统计 -->
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <i class="i-mdi-package-variant mr-2 text-theme-blue" />
              <text class="text-sm font-medium text-gray-700">已选商品</text>
            </div>
            <div class="flex items-center">
              <text class="text-lg font-bold text-theme-blue">{{ selectedGoods.length }}</text>
              <text class="text-sm text-gray-500 ml-1">件</text>
            </div>
          </div>

          <!-- 已选商品列表预览 -->
          <div v-if="selectedGoods.length > 0" class="mb-3">
            <div class="flex flex-wrap gap-1 max-h-16 overflow-hidden">
              <div v-for="item in selectedGoods.slice(0, 6)" :key="item.id" class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                {{ item.bzmc }}
              </div>
              <div v-if="selectedGoods.length > 6" class="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">+{{ selectedGoods.length - 6 }}</div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-3">
            <div
              @click="handleClearSelection"
              class="flex-1 py-2.5 px-4 flex items-center justify-center gap-2 tracking-widest bg-gray-100 text-gray-600 rounded-lg text-sm transition-all duration-200 active:bg-gray-200 active:scale-95"
              :disabled="selectedGoods.length === 0"
              :class="{ 'opacity-50 cursor-not-allowed': selectedGoods.length === 0 }"
            >
              <i class="i-mdi-close-circle" />
              <text>清空</text>
            </div>
            <div
              @click="handleConfirmSelection"
              class="flex-1 py-2.5 px-4 flex items-center justify-center gap-2 tracking-widest bg-gradient-to-r from-theme-blue to-blue-500 text-white rounded-lg text-sm shadow-md transition-all duration-200 active:shadow-lg active:scale-95"
              :disabled="selectedGoods.length === 0"
              :class="{ 'opacity-50 cursor-not-allowed': selectedGoods.length === 0 }"
            >
              <i class="i-mdi-check-circle" />
              <text>确认选择</text>
            </div>
          </div>
        </div>
      </view>
    </ui-popup>
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { MedicinalItem } from '@/monkey/types';

  // 扩展的商品类型，包含数量和重量
  interface ExtendedMedicinalItem extends MedicinalItem {
    quantity: number;
    weight: number;
  }

  // 空状态图片
  const image = ref(monkey.$url.cdn(monkey.$config.empty.noContent));

  // 是否显示商品选择器
  const showGoodsSelector = ref(false);

  // 药材列表
  const goodsList = ref<MedicinalItem[]>([]);

  // 药材列表
  const goodsListLetter = ref<MedicinalItem[]>([]);

  // 索引列表
  const goodsIndexList = ref<string[]>([]);

  // 搜索值
  const goodsSearchValue = ref<string>('');

  // 已选商品列表（用于选择器中的临时选择）
  const selectedGoods = ref<MedicinalItem[]>([]);

  // 确认的商品列表（包含数量和重量信息）
  const confirmedGoods = ref<ExtendedMedicinalItem[]>([]);

  // 滚动位置控制
  const scrollTop = ref(0);

  // 定义触发事件
  const emit = defineEmits<{
    (e: 'itemClick', item: MedicinalItem): void;
    (e: 'confirm', items: ExtendedMedicinalItem[]): void;
    (e: 'update', items: ExtendedMedicinalItem[]): void;
  }>();

  /**
   * 检查商品是否已选中
   * @param id 商品ID
   */
  const isSelected = (id: string | number) => {
    return selectedGoods.value.some((item) => item.id === id);
  };

  /**
   * 处理药材项点击事件（多选模式）
   * @param item 药材项
   */
  const handleGoodsItemClick = (item: MedicinalItem) => {
    const index = selectedGoods.value.findIndex((selected) => selected.id === item.id);

    if (index > -1) {
      // 如果已选中，则取消选中
      selectedGoods.value.splice(index, 1);
    } else {
      // 如果未选中，则添加到选中列表
      selectedGoods.value.push(item);
    }

    // 保持原有的单个点击事件
    emit('itemClick', item);
  };

  /**
   * 清空选择
   */
  const handleClearSelection = () => {
    selectedGoods.value = [];
  };

  /**
   * 确认选择
   */
  const handleConfirmSelection = () => {
    if (selectedGoods.value.length > 0) {
      // 将选中的商品转换为扩展类型，添加默认数量和重量
      const newGoods: ExtendedMedicinalItem[] = selectedGoods.value.map((item) => ({
        ...item,
        quantity: 1,
        weight: 0,
      }));

      // 合并到确认商品列表，避免重复
      newGoods.forEach((newItem) => {
        const existingIndex = confirmedGoods.value.findIndex((item) => item.id === newItem.id);
        if (existingIndex === -1) {
          confirmedGoods.value.push(newItem);
        }
      });

      console.log('🚀 ~ handleConfirmSelection ~ confirmedGoods.value:', confirmedGoods.value);

      // 触发更新事件
      emit('update', [...confirmedGoods.value]);

      showGoodsSelector.value = false;
      // 清空选择状态
      selectedGoods.value = [];
    } else {
      monkey.$helper.toast.warning('请选择商品');
    }
  };

  /**
   * 移除商品
   * @param index 商品索引
   */
  const handleRemoveGood = (index: number) => {
    confirmedGoods.value.splice(index, 1);
    emit('update', [...confirmedGoods.value]);
  };

  /**
   * 处理数量变化
   * @param index 商品索引
   * @param value 新的数量值
   */
  const handleQuantityChange = (index: number, value: number) => {
    confirmedGoods.value[index].quantity = value;
    emit('update', [...confirmedGoods.value]);
  };

  /**
   * 验证重量输入
   * @param index 商品索引
   */
  const validateWeight = (index: number) => {
    const item = confirmedGoods.value[index];
    if (item.weight < 0 || isNaN(item.weight)) {
      item.weight = 0;
    }
    // 保留一位小数
    item.weight = Math.round(item.weight * 10) / 10;
    emit('update', [...confirmedGoods.value]);
  };

  /**
   * 获取药材列表
   */
  const getMedicinalItemList = async () => {
    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemList();
      if (errcode == 0) {
        // 将数据转换为MedicinalItem类型
        goodsList.value = data;
        // 将数据转换为索引列表
        goodsListLetter.value = createLetterList(data);
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * 创建索引列表
   * @param data 药材列表
   * @returns
   */
  const createLetterList = (data: MedicinalItem[]) => {
    // 按首字母分组
    const groupedData = {};

    data.forEach((item) => {
      // 从ywsc字段中提取拼音缩写
      const ywscParts = item.ywsc.split(',');

      // 尝试找到拼音缩写（通常是纯小写字母的部分）
      let pinyin = '';
      for (const part of ywscParts) {
        // 检查是否是拼音缩写（纯小写字母）
        if (/^[a-z]+$/.test(part.trim())) {
          pinyin = part.trim();
          break;
        }
      }

      // 如果没找到拼音，使用药材名称的首字母
      const firstLetter = pinyin.charAt(0).toUpperCase() || item.bzmc.charAt(0).toUpperCase();

      // 如果该首字母组不存在，则创建
      if (!groupedData[firstLetter]) {
        groupedData[firstLetter] = [];
      }

      // 将药材添加到对应首字母组，并添加提取的拼音
      const enrichedItem = {
        ...item,
        pinyin: pinyin, // 添加提取出的拼音
      };

      groupedData[firstLetter].push(enrichedItem);
    });

    // 转换为list格式
    const formattedList = Object.keys(groupedData)
      .sort()
      .map((letter) => ({
        letter,
        data: groupedData[letter],
      }));

    goodsIndexList.value = formattedList.map((item) => item.letter);
    return formattedList;
  };

  /**
   * 过滤列表
   * @param list 药材列表
   * @returns
   */
  const filterGoodsList = (list: MedicinalItem[]) => {
    const searchTerm = goodsSearchValue.value.toLowerCase().trim();

    if (!searchTerm) {
      return list;
    }

    return list.filter((item) => {
      // 匹配药材名称
      const nameMatch = item.bzmc.toLowerCase().includes(searchTerm);

      // 匹配拼音（如果存在）
      const pinyinMatch = item.pinyin && item.pinyin.toLowerCase().includes(searchTerm);

      // 从ywsc字段中提取完整拼音进行匹配
      let ywscPinyinMatch = false;
      let pinyinInitialMatch = false;

      if (item.ywsc) {
        const ywscParts = item.ywsc.split(',');

        ywscParts.forEach((part) => {
          const trimmedPart = part.trim().toLowerCase();

          // 检查是否是拼音（纯字母）
          if (/^[a-z]+$/.test(trimmedPart)) {
            // 完整拼音匹配
            if (trimmedPart.includes(searchTerm)) {
              ywscPinyinMatch = true;
            }

            // 拼音首字母缩写匹配（例如：baishao -> bs）
            if (trimmedPart.length > 1) {
              // 尝试按常见的拼音分割方式提取首字母
              const initials = extractPinyinInitials(trimmedPart);
              if (initials.includes(searchTerm)) {
                pinyinInitialMatch = true;
              }
            }
          }
        });
      }

      // 如果item.pinyin存在，也检查其首字母缩写
      if (item.pinyin && !pinyinInitialMatch) {
        const initials = extractPinyinInitials(item.pinyin.toLowerCase());
        pinyinInitialMatch = initials.includes(searchTerm);
      }

      return nameMatch || pinyinMatch || pinyinInitialMatch || ywscPinyinMatch;
    });
  };

  /**
   * 提取拼音首字母缩写
   * @param pinyin 拼音字符串
   * @returns 首字母缩写
   */
  const extractPinyinInitials = (pinyin: string) => {
    // 常见的拼音分割模式
    const patterns = [
      // 双字拼音模式：baishao -> bs
      /^([a-z]{2,})([a-z]{2,})$/,
      // 三字拼音模式：chuanxiong -> cx (取首尾), cxx (取每个音节首字母)
      /^([a-z]{2,})([a-z]{2,})([a-z]{2,})$/,
      // 更复杂的模式可以根据需要添加
    ];

    const results = [];

    // 尝试双字模式
    const doubleMatch = pinyin.match(/^([a-z]{2,})([a-z]{2,})$/);
    if (doubleMatch) {
      const [, first, second] = doubleMatch;
      results.push(first.charAt(0) + second.charAt(0)); // bs
    }

    // 尝试三字模式
    const tripleMatch = pinyin.match(/^([a-z]{2,})([a-z]{2,})([a-z]{2,})$/);
    if (tripleMatch) {
      const [, first, second, third] = tripleMatch;
      results.push(first.charAt(0) + second.charAt(0) + third.charAt(0)); // 每个音节首字母
      results.push(first.charAt(0) + third.charAt(0)); // 首尾字母
    }

    // 简单的首字母提取（适用于较短的拼音）
    if (pinyin.length >= 2 && pinyin.length <= 8) {
      // 尝试按2个字母一组分割
      for (let i = 0; i < pinyin.length - 1; i += 2) {
        if (i + 1 < pinyin.length) {
          const syllable = pinyin.substring(i, i + 2);
          if (syllable.length === 2) {
            results.push(syllable.charAt(0));
          }
        }
      }

      // 如果是偶数长度，尝试提取首字母组合
      if (pinyin.length % 2 === 0) {
        let initials = '';
        for (let i = 0; i < pinyin.length; i += 2) {
          initials += pinyin.charAt(i);
        }
        if (initials.length > 1) {
          results.push(initials);
        }
      }
    }

    return [...new Set(results)]; // 去重
  };

  /**
   * 重置滚动位置到顶部
   */
  const resetScrollPosition = () => {
    scrollTop.value = 0;
    // 强制触发滚动更新
    nextTick(() => {
      scrollTop.value = 1;
      nextTick(() => {
        scrollTop.value = 0;
      });
    });
  };

  /**
   * 搜索
   */
  const handleGoodsSearch = () => {
    const result = filterGoodsList(goodsList.value);
    if (result.length > 0) {
      // 过滤列表
      goodsListLetter.value = createLetterList(result);
      // 重置滚动位置
      resetScrollPosition();
    } else monkey.$helper.toast.warning('暂无数据');
  };

  /**
   * 清空搜索
   */
  const handleGoodsClear = () => {
    goodsListLetter.value = createLetterList(goodsList.value);
    // 重置滚动位置
    resetScrollPosition();
  };

  // 处理选择商品事件
  const handleSelectGoods = () => {
    showGoodsSelector.value = true;
  };

  onMounted(() => {
    getMedicinalItemList();
  });
</script>

<style lang="scss" scoped></style>
