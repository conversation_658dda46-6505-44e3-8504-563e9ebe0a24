<template>
  <ly-layout>
    <div class="px-2 py-4 bg-white">
      <uni-steps :options="steps" :active="active" />
    </div>
    <div class="flex-1 pt-4 box-border">
      <swiper class="size-full" :current="active" @change="handleChange" :disable-touch="true">
        <!-- 预约时间 -->
        <swiper-item class="size-full">
          <view class="p-4 bg-white mb-4">
            <div class="mb-4 flex items-center justify-between">
              <div class="mb-4">
                <uni-section title="选择预约时间" type="line" padding="0"></uni-section>
              </div>
              <div v-if="selectedDate" class="text-sm text-gray-500">{{ selectedDate }}</div>
            </div>
            <!-- 日期选择横向滚动 -->
            <scroll-view scroll-x>
              <Dates :currentDates="currentDates.slice(0, 4)" :selectedDateIndex="selectedDateIndex" @selectDate="handleSelectDate" @more="handleMoreDates" />
            </scroll-view>
          </view>
          <!-- 显示时间段上午 -->
          <view class="p-4 bg-white mb-4">
            <div class="mb-4">
              <uni-section title="上午 选择时间段" type="line" padding="0"></uni-section>
            </div>
            <view class="grid grid-cols-3 gap-2">
              <view v-for="(item, index) in selectTimesList.sw" :key="index">
                <view
                  @click="handleSelectTimeAM(item, index)"
                  :class="selectedTimeAMIndex === index ? 'bg-theme-blue text-white' : 'bg-gray-100'"
                  class="px-3.5 py-2 text-sm text-gray-500 transition-all duration-300 box-border border border-gray-200 rounded-lg flex items-center justify-center"
                >
                  {{ item.time }}
                </view>
              </view>
            </view>
          </view>
          <!-- 显示时间段下午 -->
          <view class="p-4 bg-white">
            <div class="mb-4">
              <uni-section title="下午 选择时间段" type="line" padding="0"></uni-section>
            </div>
            <view class="grid grid-cols-3 gap-2">
              <view v-for="(item, index) in selectTimesList.xw" :key="index">
                <view
                  @click="handleSelectTimePM(item, index)"
                  :class="selectedTimePMIndex === index ? 'bg-theme-blue text-white' : 'bg-gray-100'"
                  class="px-3.5 py-2 text-sm text-gray-500 transition-all duration-300 box-border border border-gray-200 rounded-lg flex items-center justify-center"
                >
                  {{ item.time }}
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
        <!-- 物品选择 -->
        <swiper-item class="size-full">
          <view class="p-4 bg-white mb-4">
            <Goods @selectGoods="showProductSelector" />
          </view>
        </swiper-item>
      </swiper>
      <view class="p-4" v-if="active === 111">
        <view class="mb-4">
          <uni-forms ref="formRef" :model="formData" :rules="rules" label-position="top" border labelWidth="120px">
            <!-- 基本信息 -->
            <view class="p-4 mb-4 bg-white rounded-lg shadow-md">
              <view class="text-sm font-medium text-gray-800 mb-4 flex items-center gap-2">
                <div class="w-1 h-3 bg-theme-blue rounded-full"></div>
                <text>基本信息</text>
              </view>

              <view>
                <!-- 入库日期 -->
                <uni-forms-item name="yyrq" label="预约日期" required>
                  <uni-datetime-picker :start="monkey.$dayjs().format('YYYY-MM-DD')" :border="false" type="date" v-model="formData.yyrq" :clear-icon="false" />
                </uni-forms-item>

                <!-- 入库时间段 -->
                <!-- <uni-forms-item name="timeSlot" label="预约时间段" required>
                <uni-data-checkbox v-model="formData.timeSlot" :localdata="timeSlotOptions" mode="tag" :border="false" />
              </uni-forms-item> -->

                <!-- 联系人 -->
                <uni-forms-item name="yyr" label="联系人" required>
                  <uni-easyinput v-model="formData.yyr" placeholder="请输入联系人姓名" trim="both" prefixIcon="person" :inputBorder="false" />
                </uni-forms-item>

                <!-- 身份证 -->
                <uni-forms-item name="yyrsfz" label="身份证" required>
                  <uni-easyinput v-model="formData.yyrsfz" placeholder="请输入身份证" trim="both" prefixIcon="email" :inputBorder="false" />
                </uni-forms-item>

                <!-- 联系电话 -->
                <uni-forms-item name="yyrdh" label="联系电话" required>
                  <uni-easyinput v-model="formData.yyrdh" placeholder="请输入联系电话" trim="both" prefixIcon="phone" :inputBorder="false" />
                </uni-forms-item>
              </view>
            </view>
            <view class="mb-6 bg-gray-50 rounded-lg p-4 border border-gray-100">
              <view class="text-base font-medium text-gray-700 mb-4 border-l-4 border-blue-500 pl-3 flex items-center"> <text class="iconfont icon-note mr-2"></text>备注信息 </view>

              <uni-forms-item name="yuany" label="备注">
                <uni-easyinput v-model="formData.yuany" type="textarea" placeholder="请输入备注信息（选填）" :maxlength="200" :autoHeight="true" class="remark-input" />
              </uni-forms-item>
            </view>
          </uni-forms>
        </view>
      </view>
    </div>
    <ly-fixed-btns type="primary" @submit="handleSubmit(1)" icon="" rightIcon="i-mdi-arrow-right" text="下一步" v-if="active === 0"></ly-fixed-btns>
    <ly-fixed-btns v-if="active === 1" :fixed="false">
      <div class="grid grid-cols-2 gap-2">
        <div class="w-full text-sm bg-slate-500 text-white rounded-full h-44 flex items-center tracking-widest gap-2 justify-center active:bg-slate-600 transition-all duration-300" @click="handleSubmit(0)">
          <text class="text-base i-mdi-arrow-left"></text>
          <text class="text-sm">上一步</text>
        </div>
        <div class="w-full text-sm bg-theme-blue text-white rounded-full h-44 flex items-center tracking-widest gap-2 justify-center active:bg-blue-700 transition-all duration-300" @click="handleSubmit(2)">
          <text class="text-base i-mdi-check"></text>
          <text class="text-sm">确认物品</text>
        </div>
      </div>
    </ly-fixed-btns>

    <ui-popup v-model:show="showMoreDates" title="选择预约时间" position="bottom">
      <div class="p-4 box-border">
        <Dates :isMore="false" cols="4" gap="3" :currentDates="currentDates" :selectedDateIndex="selectedDateIndex" @selectDate="handleMoreSelectDate" />
      </div>
    </ui-popup>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import Dates from './components/dates.vue';
  import Goods from './components/goods.vue';
  import { HomeTypes } from '@/monkey/types';

  const { user: userInfo, hasLogin } = storeToRefs(monkey.$stores.useUserStore());

  // 表单数据
  const formData = reactive({
    yyr: '', // 预约人
    yyrdh: '', // 预约单号
    yyrsfz: '', // 预约身份证
    yyrq: '', // 预约日期
    yymx: [], // 预约明细
    yuany: '', // 预约原因
  });

  const active = ref(1);

  // 新增：选中的日期索引
  const selectedDateIndex = ref(null);

  // 新增：选中的日期
  const selectedDate = ref<string | null>(null);

  // 新增：选中的时间段
  const selectedTimeSlot = ref<string | null>('');

  // 新增：选中的时间段索引
  const selectedTimePMIndex = ref(null);

  // 新增：选中的时间段索引
  const selectedTimeAMIndex = ref(null);

  // 新增：选中的时间段列表
  const selectTimesList = ref<any[]>([]);

  // 显示更多日期弹窗
  const showMoreDates = ref<boolean>(false);

  const steps = ref([
    {
      title: '预约时间',
    },
    {
      title: '物品选择',
    },
    {
      title: '预约信息',
    },
    {
      title: '订单确认',
    },
  ]);

  const currentDates = ref<HomeTypes.InboundDateItem[]>([
    { week: '周四', date: '07-24', dateStr: '2025-07-24' },
    { week: '周五', date: '07-25', dateStr: '2025-07-25' },
    { week: '周六', date: '07-26', dateStr: '2025-07-26' },
    { week: '周日', date: '07-27', dateStr: '2025-07-27' },
    { week: '周一', date: '07-28', dateStr: '2025-07-28' },
    { week: '周二', date: '07-29', dateStr: '2025-07-29' },
    { week: '周三', date: '07-30', dateStr: '2025-07-30' },
    { week: '周四', date: '07-31', dateStr: '2025-07-31' },
    { week: '周五', date: '08-01', dateStr: '2025-08-01' },
    { week: '周六', date: '08-02', dateStr: '2025-08-02' },
    { week: '周日', date: '08-03', dateStr: '2025-08-03' },
    { week: '周一', date: '08-04', dateStr: '2025-08-04' },
    { week: '周二', date: '08-05', dateStr: '2025-08-05' },
    { week: '周三', date: '08-06', dateStr: '2025-08-06' },
  ]);

  const currentTimes = ref<{ date: string; sw: { time: string; count: number }[]; xw: { time: string; count: number }[] }[]>([
    {
      date: '07-24',
      sw: [
        { time: '09:00-12:00', count: 10 },
        { time: '13:00-17:00', count: 10 },
        { time: '18:00-21:00', count: 10 },
      ],
      xw: [
        { time: '09:00-12:00', count: 10 },
        { time: '13:00-17:00', count: 10 },
        { time: '18:00-21:00', count: 10 },
      ],
    },
    { date: '07-25', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-26', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-27', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-28', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-29', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-30', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-31', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '08-01', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '08-02', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
  ]);

  const handleSelectDate = (date, index) => {
    console.log('handleSelectDate', date, index);
    selectedDateIndex.value = index;

    // 如果有日期，设置表单日期值
    if (date) {
      // 假设年份是当前年份
      formData.yyrq = date.dateStr;
      selectedDate.value = date.dateStr;
    }
    showMoreDates.value = false;

    // 获取当前日期的时间段
    const currentDate = currentTimes.value.find((item) => item.date === date.date);
    if (currentDate) {
      selectTimesList.value = currentDate;
    }
  };

  const handleMoreSelectDate = (date, index) => {
    console.log('handleMoreSelectDate', date, index);
    selectedDateIndex.value = index;
    selectedDate.value = date.date;
    showMoreDates.value = false;
    // 获取当前日期的时间段
    const currentDate = currentTimes.value.find((item) => item.date === date.date);
    if (currentDate) {
      selectTimesList.value = currentDate;
    }
  };

  // 处理更多日期按钮点击
  const handleMoreDates = () => {
    // 打开日历选择器
    showMoreDates.value = true;
  };

  const handleChange = (e) => {
    console.log('handleChange', e);
  };

  const handleSelectTimeAM = (item, index) => {
    selectedTimePMIndex.value = null;
    selectedTimeAMIndex.value = index;
    selectedTimeSlot.value = item.time;
  };

  const handleSelectTimePM = (item, index) => {
    selectedTimeAMIndex.value = null;
    selectedTimePMIndex.value = index;
    selectedTimeSlot.value = item.time;
  };

  const handleSubmit = (index) => {
    // 如果未登录，则打开登录弹窗
    if (!hasLogin.value) {
      return monkey.$stores.useAuthModalStore().open();
    }

    // 如果未选择日期，则打开日期选择弹窗
    console.log('🚀 ~ handleSubmit ~ selectedDate.value:', selectedDate.value);
    if (!selectedDate.value) {
      return monkey.$helper.toast.error('请选择预约日期');
    }

    // 如果未选择时间段，则打开时间段选择弹窗
    if (!selectedTimeSlot.value) {
      return monkey.$helper.toast.error('请选择预约时间段');
    }

    // 如果未选择物品，则打开物品选择弹窗

    active.value = index;
    console.log('🚀 ~ handleNextStep ~ active.value:', active.value);
  };

  const handleSubmitInbound = () => {
    if (hasLogin.value) {
    }
    console.log('handleSubmitInbound');
  };

  onLoad(() => {
    console.log('onLoad');
    if (hasLogin.value) {
      // formData.yyr = userInfo.value?.zsxm;
      // formData.yyrsfz = userInfo.value?.sfzhm;
      // formData.yyrdh = userInfo.value?.sjh;
      // formData.yyrq = monkey.$dayjs().format('YYYY-MM-DD');
    }
    handleSelectDate(currentDates.value[0], 0);
  });

  // 表单引用
  const formRef = ref();

  // 商品选择弹窗引用
  const productSelector = ref();

  // 时间段选项
  const timeSlotOptions = [
    { text: '上午 (9:00-12:00)', value: 'morning' },
    { text: '下午 (13:00-17:00)', value: 'afternoon' },
    { text: '晚上 (18:00-21:00)', value: 'evening' },
  ];

  // 仓库选项
  const warehouseOptions = [
    { text: 'A区主仓库', value: 'A001' },
    { text: 'B区冷链仓库', value: 'B001' },
    { text: 'C区普通仓库', value: 'C001' },
    { text: 'D区特殊物品仓库', value: 'D001' },
  ];

  // 表单验证规则
  const rules = {
    reservationDate: {
      rules: [{ required: true, errorMessage: '请选择预约日期' }],
    },
    timeSlot: {
      rules: [{ required: true, errorMessage: '请选择预约时间段' }],
    },
    contactName: {
      rules: [
        { required: true, errorMessage: '请输入联系人姓名' },
        { minLength: 2, maxLength: 20, errorMessage: '联系人姓名长度在2-20个字符之间' },
      ],
    },
    contactPhone: {
      rules: [
        { required: true, errorMessage: '请输入联系电话' },
        { pattern: /^1[3-9]\d{9}$/, errorMessage: '请输入正确的手机号码' },
      ],
    },
    warehouseId: {
      rules: [{ required: true, errorMessage: '请选择入库仓库' }],
    },
    items: {
      rules: [
        {
          validateFunction: (rule, value, data, callback) => {
            if (value.length === 0) {
              callback('请至少选择一件商品');
            }
            return true;
          },
        },
      ],
    },
  };

  // 商品列表错误信息
  const itemsErrorMsg = ref('');

  // 可选商品列表
  const availableProducts = ref([
    {
      id: '1',
      name: '东北大米 五常稻花香',
      quantity: 1,
      unit: 'kg',
      category: '粮食',
      image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    },
    {
      id: '2',
      name: '优质花生米 精选大粒',
      quantity: 1,
      unit: 'kg',
      category: '坚果',
      image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    },
    {
      id: '3',
      name: '有机玉米 非转基因',
      quantity: 1,
      unit: 'kg',
      category: '粮食',
      image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    },
    {
      id: '4',
      name: '高粱米 红高粱',
      quantity: 1,
      unit: 'kg',
      category: '粮食',
      image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    },
  ]);

  // 搜索关键词
  const searchKeyword = ref('');

  // 组件初始化完成后执行
  onReady(() => {
    // 初始化表单
  });

  // 选择商品
  const selectProduct = (product) => {
    // 检查是否已经选择了该商品
    const existIndex = formData.items.findIndex((item) => item.id === product.id);

    if (existIndex === -1) {
      // 如果没有选择过，添加到列表
      formData.items.push({ ...product });

      // 添加成功提示
      uni.showToast({
        title: '已添加',
        icon: 'success',
        duration: 1000,
      });
    } else {
      // 如果已经选择过，增加数量
      formData.items[existIndex].quantity += 1;

      // 更新数量提示
      uni.showToast({
        title: '数量已更新',
        icon: 'none',
        duration: 1000,
      });
    }

    hideProductSelector();
    itemsErrorMsg.value = '';
  };

  // 移除商品
  const removeItem = (index) => {
    formData.items.splice(index, 1);

    // 删除提示
    uni.showToast({
      title: '已删除',
      icon: 'none',
      duration: 1000,
    });
  };

  // 搜索商品
  const searchProducts = () => {
    // 实现搜索逻辑
    console.log('搜索商品:', searchKeyword.value);
  };

  // 提交表单
  const submitForm = () => {
    formRef.value
      .validate()
      .then((res) => {
        console.log('表单数据验证通过', formData);

        // 显示加载提示
        uni.showLoading({
          title: '提交中...',
        });

        // 模拟请求
        setTimeout(() => {
          // 隐藏加载提示
          uni.hideLoading();

          // 显示成功提示
          uni.showToast({
            title: '预约成功',
            icon: 'success',
            duration: 2000,
          });

          // 重置表单
          formRef.value.resetFields();
          formData.items = [];

          // TODO: 跳转到预约列表或详情页
        }, 1500);
      })
      .catch((err) => {
        console.log('表单数据验证失败', err);
      });
  };

  // 新增：选择日期
  const selectDate = (index) => {
    selectedDateIndex.value = index;
    // 重置时间段选择
    selectedTimeSlot.value = '';
  };

  // 新增：获取时间段
  const getTimeSlots = (dateIndex) => {
    const date = currentDates.value[dateIndex].date;
    const timeData = currentTimes.value.find((item) => item.date === date);
    if (!timeData) return [];

    // 合并上午和下午的时间段
    return [...timeData.sw, ...timeData.xw];
  };

  // 新增：选择时间段
  const selectTimeSlot = (timeSlot) => {
    selectedTimeSlot.value = timeSlot.time;
  };

  // 新增：判断是否为今天
  const isToday = (date) => {
    const today = new Date();
    const todayStr = `${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    return date === todayStr;
  };
</script>

<style lang="scss" scoped></style>
